"use client"

import { useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Phone,
  PhoneCall,
  PhoneIncoming,
  PhoneOutgoing,
  Clock,
  Users,
  TrendingUp,
  Globe,
  BarChart3,
} from "lucide-react"

export default function CallsPage() {
  const [localCalls, setLocalCalls] = useState<any>(null)
  const [internationalCalls, setInternationalCalls] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [localRes, internationalRes] = await Promise.all([
          fetch("/api/local-call-activity.json"),
          fetch("/api/international-call-activity.json"),
        ])

        const [localData, internationalData] = await Promise.all([localRes.json(), internationalRes.json()])

        setLocalCalls(localData)
        setInternationalCalls(internationalData)
      } catch (error) {
        console.error("Error fetching call data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  const totalCalls = (localCalls?.totalCalls || 0) + (internationalCalls?.totalCalls || 0)

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Call Activity</h1>
          <p className="text-gray-400 mt-2">Comprehensive call analytics and usage patterns</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <BarChart3 className="w-4 h-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Calls</p>
                <p className="text-2xl font-bold text-blue-400">{totalCalls}</p>
              </div>
              <Phone className="w-8 h-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Outgoing</p>
                <p className="text-2xl font-bold text-green-400">
                  {(localCalls?.outgoing.count || 0) + (internationalCalls?.outgoing.count || 0)}
                </p>
              </div>
              <PhoneOutgoing className="w-8 h-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Incoming</p>
                <p className="text-2xl font-bold text-purple-400">
                  {(localCalls?.incoming.count || 0) + (internationalCalls?.incoming.count || 0)}
                </p>
              </div>
              <PhoneIncoming className="w-8 h-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Avg Duration</p>
                <p className="text-2xl font-bold text-orange-400">{localCalls?.avgDurationMin}m</p>
              </div>
              <Clock className="w-8 h-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="local" className="space-y-6">
        <TabsList className="bg-gray-900 border border-gray-800">
          <TabsTrigger value="local" className="data-[state=active]:bg-blue-600">
            Local Calls
          </TabsTrigger>
          <TabsTrigger value="international" className="data-[state=active]:bg-green-600">
            International Calls
          </TabsTrigger>
          <TabsTrigger value="analytics" className="data-[state=active]:bg-purple-600">
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="local" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-blue-400">
                  <PhoneCall className="w-5 h-5 mr-2" />
                  Local Call Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Total Local Calls</span>
                  <span className="font-mono text-2xl text-blue-400">{localCalls?.totalCalls}</span>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-400">Outgoing</span>
                    <span className="font-mono text-green-400">{localCalls?.outgoing.count}</span>
                  </div>
                  <Progress value={(localCalls?.outgoing.count / localCalls?.totalCalls) * 100} className="h-2" />

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-400">Incoming</span>
                    <span className="font-mono text-purple-400">{localCalls?.incoming.count}</span>
                  </div>
                  <Progress value={(localCalls?.incoming.count / localCalls?.totalCalls) * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-yellow-400">
                  <TrendingUp className="w-5 h-5 mr-2" />
                  Usage Patterns
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Peak Hour</span>
                  <Badge className="bg-yellow-900/20 text-yellow-400 border-yellow-400/20">
                    {localCalls?.peakHour}
                  </Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">I/O Ratio</span>
                  <span className="font-mono text-purple-400">{localCalls?.ratioIncomingOutgoing}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Distinct Ratio</span>
                  <span className="font-mono text-cyan-400">{localCalls?.distinctRatio}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Avg Duration</span>
                  <span className="font-mono text-orange-400">{localCalls?.avgDurationMin} min</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-cyan-400">
                <Users className="w-5 h-5 mr-2" />
                Contact Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-medium text-white">Outgoing Calls</h4>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Total Calls</span>
                    <span className="font-mono text-green-400">{localCalls?.outgoing.count}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Distinct Numbers</span>
                    <span className="font-mono text-green-400">{localCalls?.outgoing.distinctNumbers}</span>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium text-white">Incoming Calls</h4>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Total Calls</span>
                    <span className="font-mono text-purple-400">{localCalls?.incoming.count}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Distinct Numbers</span>
                    <span className="font-mono text-purple-400">{localCalls?.incoming.distinctNumbers}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="international" className="space-y-6">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-green-400">
                <Globe className="w-5 h-5 mr-2" />
                International Call Activity
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <p className="text-4xl font-bold text-green-400 mb-2">{internationalCalls?.totalCalls}</p>
                <p className="text-gray-400">Total International Calls</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium text-white flex items-center">
                    <PhoneOutgoing className="w-4 h-4 mr-2 text-green-400" />
                    Outgoing International
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Calls</span>
                      <span className="font-mono text-green-400">{internationalCalls?.outgoing.count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Unique Numbers</span>
                      <span className="font-mono text-green-400">{internationalCalls?.outgoing.uniqueNumbers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Countries</span>
                      <span className="font-mono text-green-400">{internationalCalls?.outgoing.countries}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-white flex items-center">
                    <PhoneIncoming className="w-4 h-4 mr-2 text-blue-400" />
                    Incoming International
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Calls</span>
                      <span className="font-mono text-blue-400">{internationalCalls?.incoming.count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Unique Numbers</span>
                      <span className="font-mono text-blue-400">{internationalCalls?.incoming.uniqueNumbers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Countries</span>
                      <span className="font-mono text-blue-400">{internationalCalls?.incoming.countries}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-purple-400">Call Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Local Calls</span>
                    <span className="font-mono text-blue-400">{localCalls?.totalCalls}</span>
                  </div>
                  <Progress value={(localCalls?.totalCalls / totalCalls) * 100} className="h-3" />

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">International Calls</span>
                    <span className="font-mono text-green-400">{internationalCalls?.totalCalls}</span>
                  </div>
                  <Progress value={(internationalCalls?.totalCalls / totalCalls) * 100} className="h-3" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-orange-400">Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Call Success Rate</span>
                  <Badge className="bg-green-900/20 text-green-400 border-green-400/20">98.5%</Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Network Quality</span>
                  <Badge className="bg-blue-900/20 text-blue-400 border-blue-400/20">Excellent</Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Drop Rate</span>
                  <span className="font-mono text-red-400">1.5%</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Avg Setup Time</span>
                  <span className="font-mono text-yellow-400">2.3s</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
