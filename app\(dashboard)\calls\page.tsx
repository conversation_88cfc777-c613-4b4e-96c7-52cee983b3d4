"use client"

import { useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Phone,
  PhoneCall,
  PhoneIncoming,
  PhoneOutgoing,
  Clock,
  Users,
  TrendingUp,
  Globe,
  BarChart3,
  AlertTriangle,
  Shield,
  Eye,
  Zap,
  Target,
  Activity,
  MapPin,
  Smartphone,
} from "lucide-react"

export default function CallsPage() {
  const [localCalls, setLocalCalls] = useState<any>(null)
  const [internationalCalls, setInternationalCalls] = useState<any>(null)
  const [imeiData, setImeiData] = useState<any>(null)
  const [subscriberProfile, setSubscriberProfile] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [localRes, internationalRes, imeiRes, subscriberRes] = await Promise.all([
          fetch("/api/enhanced-call-activity.json"),
          fetch("/api/enhanced-international-call-activity.json"),
          fetch("/api/imei-tracking.json"),
          fetch("/api/enhanced-subscriber-profile.json"),
        ])

        const [localData, internationalData, imeiDataRes, subscriberData] = await Promise.all([
          localRes.json(),
          internationalRes.json(),
          imeiRes.json(),
          subscriberRes.json()
        ])

        setLocalCalls(localData)
        setInternationalCalls(internationalData)
        setImeiData(imeiDataRes)
        setSubscriberProfile(subscriberData)
      } catch (error) {
        console.error("Error fetching call data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  const totalCalls = (localCalls?.totalCalls || 0) + (internationalCalls?.totalCalls || 0)

  // Calculate overall fraud risk score
  const calculateOverallRiskScore = () => {
    const localRisk = localCalls?.riskScore || 0
    const internationalRisk = internationalCalls?.riskScore || 0
    const imeiRisk = imeiData?.riskScore || 0
    const subscriberRisk = subscriberProfile?.fraudProfile?.overallRiskScore || 0

    return ((localRisk + internationalRisk + imeiRisk + subscriberRisk) / 4).toFixed(1)
  }

  const overallRiskScore = calculateOverallRiskScore()
  const getRiskLevel = (score: number) => {
    if (score >= 7) return { level: "HIGH", color: "text-red-400", bgColor: "bg-red-900/20", borderColor: "border-red-400/20" }
    if (score >= 4) return { level: "MEDIUM", color: "text-yellow-400", bgColor: "bg-yellow-900/20", borderColor: "border-yellow-400/20" }
    return { level: "LOW", color: "text-green-400", bgColor: "bg-green-900/20", borderColor: "border-green-400/20" }
  }

  const riskInfo = getRiskLevel(parseFloat(overallRiskScore))

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Call Activity</h1>
          <p className="text-gray-400 mt-2">Comprehensive call analytics and usage patterns</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <BarChart3 className="w-4 h-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      {/* Fraud Alert Banner */}
      {parseFloat(overallRiskScore) >= 7 && (
        <Card className="bg-red-900/20 border-red-400/20">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <AlertTriangle className="w-6 h-6 text-red-400" />
              <div>
                <p className="text-red-400 font-semibold">High Fraud Risk Detected</p>
                <p className="text-red-300 text-sm">Multiple suspicious patterns identified. Immediate review recommended.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card className={`bg-gray-900 border-gray-800 ${riskInfo.borderColor}`}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Fraud Risk Score</p>
                <p className={`text-2xl font-bold ${riskInfo.color}`}>{overallRiskScore}/10</p>
                <Badge className={`${riskInfo.bgColor} ${riskInfo.color} ${riskInfo.borderColor} text-xs mt-1`}>
                  {riskInfo.level} RISK
                </Badge>
              </div>
              <Shield className={`w-8 h-8 ${riskInfo.color}`} />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Calls</p>
                <p className="text-2xl font-bold text-blue-400">{totalCalls}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {localCalls?.fraudIndicators?.shortCallsCount || 0} short calls
                </p>
              </div>
              <Phone className="w-8 h-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">IMEI Changes</p>
                <p className="text-2xl font-bold text-orange-400">{imeiData?.fraudIndicators?.totalIMEICount || 0}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {imeiData?.fraudIndicators?.imeiSwitchFrequency || 'NORMAL'} frequency
                </p>
              </div>
              <Smartphone className="w-8 h-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Contact Network</p>
                <p className="text-2xl font-bold text-purple-400">
                  {localCalls?.contactNetwork?.totalUniqueContacts || 0}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {((localCalls?.contactNetwork?.networkDensity || 0) * 100).toFixed(0)}% density
                </p>
              </div>
              <Users className="w-8 h-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Avg Duration</p>
                <p className="text-2xl font-bold text-cyan-400">{localCalls?.avgDurationMin}m</p>
                <p className="text-xs text-gray-500 mt-1">
                  {localCalls?.callPatterns?.shortCalls?.count || 0} under 1min
                </p>
              </div>
              <Clock className="w-8 h-8 text-cyan-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="fraud-overview" className="space-y-6">
        <TabsList className="bg-gray-900 border border-gray-800">
          <TabsTrigger value="fraud-overview" className="data-[state=active]:bg-red-600">
            <AlertTriangle className="w-4 h-4 mr-2" />
            Fraud Overview
          </TabsTrigger>
          <TabsTrigger value="call-patterns" className="data-[state=active]:bg-blue-600">
            <Activity className="w-4 h-4 mr-2" />
            Call Patterns
          </TabsTrigger>
          <TabsTrigger value="device-analysis" className="data-[state=active]:bg-orange-600">
            <Smartphone className="w-4 h-4 mr-2" />
            Device Analysis
          </TabsTrigger>
          <TabsTrigger value="network-behavior" className="data-[state=active]:bg-purple-600">
            <MapPin className="w-4 h-4 mr-2" />
            Network Behavior
          </TabsTrigger>
          <TabsTrigger value="analytics" className="data-[state=active]:bg-green-600">
            <BarChart3 className="w-4 h-4 mr-2" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="fraud-overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Risk Assessment */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-red-400">
                  <AlertTriangle className="w-5 h-5 mr-2" />
                  Risk Assessment
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Overall Risk Score</span>
                  <span className={`font-mono text-2xl ${riskInfo.color}`}>{overallRiskScore}/10</span>
                </div>
                <Progress value={parseFloat(overallRiskScore) * 10} className="h-3" />

                <div className="space-y-3 mt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-400">Call Pattern Risk</span>
                    <span className="font-mono text-yellow-400">{localCalls?.riskScore || 0}/10</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-400">Device Risk</span>
                    <span className="font-mono text-orange-400">{imeiData?.riskScore || 0}/10</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-400">International Risk</span>
                    <span className="font-mono text-green-400">{internationalCalls?.riskScore || 0}/10</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Fraud Indicators */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-yellow-400">
                  <Eye className="w-5 h-5 mr-2" />
                  Active Fraud Indicators
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {localCalls?.fraudIndicators?.suspiciousPatterns?.map((pattern: string, index: number) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-yellow-900/10 rounded-lg border border-yellow-400/20">
                    <span className="text-yellow-400 text-sm capitalize">{pattern.replace('_', ' ')}</span>
                    <Badge className="bg-yellow-900/20 text-yellow-400 border-yellow-400/20">Active</Badge>
                  </div>
                ))}

                {imeiData?.alerts?.map((alert: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-red-900/10 rounded-lg border border-red-400/20">
                    <div>
                      <span className="text-red-400 text-sm font-medium">{alert.type.replace('_', ' ')}</span>
                      <p className="text-red-300 text-xs">{alert.message}</p>
                    </div>
                    <Badge className="bg-red-900/20 text-red-400 border-red-400/20">{alert.severity}</Badge>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Detailed Fraud Analysis */}
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-purple-400">
                <Target className="w-5 h-5 mr-2" />
                Detailed Fraud Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-3">
                  <h4 className="font-medium text-white">Call Behavior</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">Short Calls (&lt;1min)</span>
                      <span className="text-red-400">{localCalls?.fraudIndicators?.shortCallsCount || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">High Volume Spikes</span>
                      <span className="text-yellow-400">{localCalls?.fraudIndicators?.highVolumeSpikes || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">Max Calls/Hour</span>
                      <span className="text-orange-400">{localCalls?.fraudIndicators?.maxCallsInHour || 0}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium text-white">Device Patterns</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">IMEI Switches</span>
                      <span className="text-red-400">{imeiData?.fraudIndicators?.totalIMEICount || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">Switch Frequency</span>
                      <span className="text-yellow-400">{imeiData?.fraudIndicators?.imeiSwitchFrequency || 'NORMAL'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">Avg Usage Days</span>
                      <span className="text-orange-400">{imeiData?.fraudIndicators?.avgIMEIUsageDays || 0}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium text-white">Network Analysis</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">Unique Contacts</span>
                      <span className="text-purple-400">{localCalls?.contactNetwork?.totalUniqueContacts || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">Network Density</span>
                      <span className="text-cyan-400">{((localCalls?.contactNetwork?.networkDensity || 0) * 100).toFixed(0)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">Cell Switches</span>
                      <span className="text-green-400">{localCalls?.fraudIndicators?.geographicMovement?.cellTowerSwitches || 0}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="call-patterns" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Call Duration Analysis */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-blue-400">
                  <Clock className="w-5 h-5 mr-2" />
                  Call Duration Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Short Calls (&lt;1min)</span>
                    <div className="text-right">
                      <span className="font-mono text-red-400">{localCalls?.callPatterns?.shortCalls?.count || 0}</span>
                      <p className="text-xs text-gray-500">Avg: {localCalls?.callPatterns?.shortCalls?.avgDuration || 0}min</p>
                    </div>
                  </div>
                  <Progress value={(localCalls?.callPatterns?.shortCalls?.count / localCalls?.totalCalls) * 100} className="h-2" />

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Normal Calls (1-5min)</span>
                    <div className="text-right">
                      <span className="font-mono text-green-400">{localCalls?.callPatterns?.normalCalls?.count || 0}</span>
                      <p className="text-xs text-gray-500">Avg: {localCalls?.callPatterns?.normalCalls?.avgDuration || 0}min</p>
                    </div>
                  </div>
                  <Progress value={(localCalls?.callPatterns?.normalCalls?.count / localCalls?.totalCalls) * 100} className="h-2" />

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Long Calls (&gt;5min)</span>
                    <div className="text-right">
                      <span className="font-mono text-blue-400">{localCalls?.callPatterns?.longCalls?.count || 0}</span>
                      <p className="text-xs text-gray-500">Avg: {localCalls?.callPatterns?.longCalls?.avgDuration || 0}min</p>
                    </div>
                  </div>
                  <Progress value={(localCalls?.callPatterns?.longCalls?.count / localCalls?.totalCalls) * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>

            {/* Time Pattern Analysis */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-purple-400">
                  <Activity className="w-5 h-5 mr-2" />
                  Time Pattern Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Peak Hour Activity</span>
                  <Badge className="bg-yellow-900/20 text-yellow-400 border-yellow-400/20">
                    {localCalls?.peakHour}
                  </Badge>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Peak Hour Calls</span>
                    <span className="font-mono text-yellow-400">{localCalls?.timeAnalysis?.peakHourCalls || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Off-Peak Calls</span>
                    <span className="font-mono text-blue-400">{localCalls?.timeAnalysis?.offPeakCalls || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Night Calls (10PM-6AM)</span>
                    <span className="font-mono text-orange-400">{localCalls?.timeAnalysis?.nightCalls || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Weekend Calls</span>
                    <span className="font-mono text-purple-400">{localCalls?.timeAnalysis?.weekendCalls || 0}</span>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-blue-900/10 rounded-lg border border-blue-400/20">
                  <p className="text-blue-400 text-sm font-medium">Pattern Analysis</p>
                  <p className="text-blue-300 text-xs">
                    {localCalls?.fraudIndicators?.unusualPeakActivity ?
                      "Unusual peak hour activity detected" :
                      "Normal time distribution pattern"
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Contact Network Analysis */}
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-cyan-400">
                <Users className="w-5 h-5 mr-2" />
                Contact Network Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium text-white flex items-center">
                    <PhoneOutgoing className="w-4 h-4 mr-2 text-green-400" />
                    Outgoing Call Patterns
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Total Outgoing</span>
                      <span className="font-mono text-green-400">{localCalls?.outgoing.count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Distinct Numbers</span>
                      <span className="font-mono text-green-400">{localCalls?.outgoing.distinctNumbers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Repeat Call Ratio</span>
                      <span className="font-mono text-yellow-400">
                        {((localCalls?.outgoing.count - localCalls?.outgoing.distinctNumbers) / localCalls?.outgoing.count * 100).toFixed(0)}%
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-white flex items-center">
                    <PhoneIncoming className="w-4 h-4 mr-2 text-blue-400" />
                    Incoming Call Patterns
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Total Incoming</span>
                      <span className="font-mono text-blue-400">{localCalls?.incoming.count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Distinct Numbers</span>
                      <span className="font-mono text-blue-400">{localCalls?.incoming.distinctNumbers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">I/O Ratio</span>
                      <span className="font-mono text-purple-400">{localCalls?.ratioIncomingOutgoing}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-purple-900/10 rounded-lg border border-purple-400/20">
                <div className="flex justify-between items-center mb-3">
                  <h5 className="text-purple-400 font-medium">Network Behavior Assessment</h5>
                  <Badge className={`${riskInfo.bgColor} ${riskInfo.color} ${riskInfo.borderColor}`}>
                    {localCalls?.riskLevel || 'UNKNOWN'}
                  </Badge>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Network Density</span>
                    <p className="text-purple-400 font-mono">{((localCalls?.contactNetwork?.networkDensity || 0) * 100).toFixed(0)}%</p>
                  </div>
                  <div>
                    <span className="text-gray-400">Frequent Contacts</span>
                    <p className="text-cyan-400 font-mono">{localCalls?.contactNetwork?.frequentContacts || 0}</p>
                  </div>
                  <div>
                    <span className="text-gray-400">One-time Contacts</span>
                    <p className="text-orange-400 font-mono">{localCalls?.contactNetwork?.oneTimeContacts || 0}</p>
                  </div>
                  <div>
                    <span className="text-gray-400">Circular Calling</span>
                    <p className={`font-mono ${localCalls?.contactNetwork?.circularCalling ? 'text-red-400' : 'text-green-400'}`}>
                      {localCalls?.contactNetwork?.circularCalling ? 'Detected' : 'None'}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="device-analysis" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* IMEI Tracking */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-orange-400">
                  <Smartphone className="w-5 h-5 mr-2" />
                  IMEI Tracking & Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Current IMEI</span>
                  <span className="font-mono text-orange-400 text-sm">{imeiData?.currentIMEI || 'N/A'}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Total IMEI Count</span>
                  <span className="font-mono text-red-400 text-xl">{imeiData?.fraudIndicators?.totalIMEICount || 0}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Switch Frequency</span>
                  <Badge className={`${
                    imeiData?.fraudIndicators?.imeiSwitchFrequency === 'HIGH' ? 'bg-red-900/20 text-red-400 border-red-400/20' :
                    imeiData?.fraudIndicators?.imeiSwitchFrequency === 'MEDIUM' ? 'bg-yellow-900/20 text-yellow-400 border-yellow-400/20' :
                    'bg-green-900/20 text-green-400 border-green-400/20'
                  }`}>
                    {imeiData?.fraudIndicators?.imeiSwitchFrequency || 'NORMAL'}
                  </Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Avg Usage per IMEI</span>
                  <span className="font-mono text-yellow-400">{imeiData?.fraudIndicators?.avgIMEIUsageDays || 0} days</span>
                </div>

                <div className="mt-4 p-3 bg-orange-900/10 rounded-lg border border-orange-400/20">
                  <p className="text-orange-400 text-sm font-medium">Risk Assessment</p>
                  <p className="text-orange-300 text-xs">
                    {imeiData?.fraudIndicators?.rapidSwitching ?
                      "Rapid IMEI switching detected - High fraud risk" :
                      "Normal IMEI usage pattern"
                    }
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Device History */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-cyan-400">
                  <Activity className="w-5 h-5 mr-2" />
                  Device Usage History
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {imeiData?.imeiHistory?.slice(0, 3).map((device: any, index: number) => (
                  <div key={index} className="p-3 bg-gray-800/50 rounded-lg border border-gray-700">
                    <div className="flex justify-between items-start mb-2">
                      <span className="font-mono text-cyan-400 text-sm">{device.imei}</span>
                      <Badge className={`${device.status === 'active' ? 'bg-green-900/20 text-green-400 border-green-400/20' : 'bg-gray-700 text-gray-400'}`}>
                        {device.status}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="text-gray-400">Calls:</span>
                        <span className="text-white ml-1">{device.callCount}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Duration:</span>
                        <span className="text-white ml-1">
                          {Math.ceil((new Date(device.lastSeen).getTime() - new Date(device.firstSeen).getTime()) / (1000 * 60 * 60 * 24))} days
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Device Analysis Details */}
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-purple-400">
                <Target className="w-5 h-5 mr-2" />
                Device Behavior Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-3">
                  <h4 className="font-medium text-white">Device Types</h4>
                  <div className="space-y-2">
                    {imeiData?.deviceAnalysis?.deviceTypes?.map((device: string, index: number) => (
                      <div key={index} className="flex justify-between items-center">
                        <span className="text-gray-400 text-sm">{device}</span>
                        <Badge className="bg-blue-900/20 text-blue-400 border-blue-400/20 text-xs">
                          {index === 0 ? 'Current' : 'Previous'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium text-white">Fraud Indicators</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">Suspicious IMEIs</span>
                      <span className="text-red-400">{imeiData?.fraudIndicators?.suspiciousIMEIs || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">Blacklisted IMEIs</span>
                      <span className="text-red-400">{imeiData?.fraudIndicators?.blacklistedIMEIs || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">Inconsistent Types</span>
                      <span className={`${imeiData?.deviceAnalysis?.inconsistentDeviceTypes ? 'text-red-400' : 'text-green-400'}`}>
                        {imeiData?.deviceAnalysis?.inconsistentDeviceTypes ? 'Yes' : 'No'}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium text-white">Risk Metrics</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">Device Risk Score</span>
                      <span className="text-orange-400 font-mono">{imeiData?.riskScore || 0}/10</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">Rapid Switching</span>
                      <span className={`${imeiData?.fraudIndicators?.rapidSwitching ? 'text-red-400' : 'text-green-400'}`}>
                        {imeiData?.fraudIndicators?.rapidSwitching ? 'Detected' : 'Normal'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">Stolen Indicators</span>
                      <span className={`${imeiData?.deviceAnalysis?.stolenDeviceIndicators ? 'text-red-400' : 'text-green-400'}`}>
                        {imeiData?.deviceAnalysis?.stolenDeviceIndicators ? 'Present' : 'None'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Alerts Section */}
              {imeiData?.alerts && imeiData.alerts.length > 0 && (
                <div className="mt-6 p-4 bg-red-900/10 rounded-lg border border-red-400/20">
                  <h5 className="text-red-400 font-medium mb-3 flex items-center">
                    <AlertTriangle className="w-4 h-4 mr-2" />
                    Active Device Alerts
                  </h5>
                  <div className="space-y-2">
                    {imeiData.alerts.map((alert: any, index: number) => (
                      <div key={index} className="flex justify-between items-center p-2 bg-red-900/20 rounded">
                        <div>
                          <span className="text-red-400 text-sm font-medium">{alert.type.replace('_', ' ')}</span>
                          <p className="text-red-300 text-xs">{alert.message}</p>
                        </div>
                        <Badge className="bg-red-900/30 text-red-400 border-red-400/30">{alert.severity}</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="network-behavior" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Geographic Movement */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-green-400">
                  <MapPin className="w-5 h-5 mr-2" />
                  Geographic Movement Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Cell Tower Switches</span>
                  <span className="font-mono text-green-400">{localCalls?.fraudIndicators?.geographicMovement?.cellTowerSwitches || 0}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Max Speed</span>
                  <span className="font-mono text-blue-400">{localCalls?.fraudIndicators?.geographicMovement?.maxSpeedKmh || 0} km/h</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Impossible Movement</span>
                  <Badge className={`${localCalls?.fraudIndicators?.geographicMovement?.impossibleMovement ? 'bg-red-900/20 text-red-400 border-red-400/20' : 'bg-green-900/20 text-green-400 border-green-400/20'}`}>
                    {localCalls?.fraudIndicators?.geographicMovement?.impossibleMovement ? 'Detected' : 'None'}
                  </Badge>
                </div>

                <div className="mt-4 p-3 bg-green-900/10 rounded-lg border border-green-400/20">
                  <p className="text-green-400 text-sm font-medium">Movement Pattern</p>
                  <p className="text-green-300 text-xs">
                    {localCalls?.fraudIndicators?.geographicMovement?.impossibleMovement ?
                      "Suspicious movement patterns detected" :
                      "Normal geographic movement pattern"
                    }
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* International Activity */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-blue-400">
                  <Globe className="w-5 h-5 mr-2" />
                  International Activity
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">International Calls</span>
                  <span className="font-mono text-blue-400">{internationalCalls?.totalCalls || 0}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Countries Contacted</span>
                  <span className="font-mono text-purple-400">
                    {(internationalCalls?.outgoing?.countries || 0) + (internationalCalls?.incoming?.countries || 0)}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">High-Risk Countries</span>
                  <span className="font-mono text-red-400">{internationalCalls?.fraudIndicators?.highRiskCountries?.length || 0}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">International Risk</span>
                  <Badge className={`${
                    internationalCalls?.riskLevel === 'HIGH' ? 'bg-red-900/20 text-red-400 border-red-400/20' :
                    internationalCalls?.riskLevel === 'MEDIUM' ? 'bg-yellow-900/20 text-yellow-400 border-yellow-400/20' :
                    'bg-green-900/20 text-green-400 border-green-400/20'
                  }`}>
                    {internationalCalls?.riskLevel || 'LOW'}
                  </Badge>
                </div>

                {internationalCalls?.fraudIndicators?.highRiskCountries?.length > 0 && (
                  <div className="mt-4 p-3 bg-red-900/10 rounded-lg border border-red-400/20">
                    <p className="text-red-400 text-sm font-medium">High-Risk Destinations</p>
                    <div className="mt-2 space-y-1">
                      {internationalCalls.fraudIndicators.highRiskCountries.map((country: string, index: number) => (
                        <span key={index} className="inline-block bg-red-900/20 text-red-400 text-xs px-2 py-1 rounded mr-2">
                          {country}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Network Usage Patterns */}
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-purple-400">
                <Activity className="w-5 h-5 mr-2" />
                Network Usage Patterns
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium text-white">Call Distribution</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Local Calls</span>
                      <span className="font-mono text-blue-400">{localCalls?.totalCalls}</span>
                    </div>
                    <Progress value={(localCalls?.totalCalls / totalCalls) * 100} className="h-2" />

                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">International Calls</span>
                      <span className="font-mono text-green-400">{internationalCalls?.totalCalls}</span>
                    </div>
                    <Progress value={(internationalCalls?.totalCalls / totalCalls) * 100} className="h-2" />
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-white">Fraud Risk Factors</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">Call Volume Spikes</span>
                      <span className="text-yellow-400">{localCalls?.fraudIndicators?.highVolumeSpikes || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">No Incoming Periods</span>
                      <span className="text-orange-400">{localCalls?.fraudIndicators?.noIncomingCallsPeriods || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">Unusual Peak Activity</span>
                      <span className={`${localCalls?.fraudIndicators?.unusualPeakActivity ? 'text-red-400' : 'text-green-400'}`}>
                        {localCalls?.fraudIndicators?.unusualPeakActivity ? 'Yes' : 'No'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-purple-900/10 rounded-lg border border-purple-400/20">
                <h5 className="text-purple-400 font-medium mb-3">Network Behavior Summary</h5>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Avg Calls/Hour</span>
                    <p className="text-purple-400 font-mono">{localCalls?.fraudIndicators?.avgCallsPerHour || 0}</p>
                  </div>
                  <div>
                    <span className="text-gray-400">Max Calls/Hour</span>
                    <p className="text-orange-400 font-mono">{localCalls?.fraudIndicators?.maxCallsInHour || 0}</p>
                  </div>
                  <div>
                    <span className="text-gray-400">Distinct Ratio</span>
                    <p className="text-cyan-400 font-mono">{localCalls?.distinctRatio}</p>
                  </div>
                  <div>
                    <span className="text-gray-400">Duration Variance</span>
                    <p className="text-yellow-400 font-mono">{localCalls?.fraudIndicators?.callDurationVariance || 0}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-blue-400">
                  <PhoneCall className="w-5 h-5 mr-2" />
                  Local Call Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Total Local Calls</span>
                  <span className="font-mono text-2xl text-blue-400">{localCalls?.totalCalls}</span>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-400">Outgoing</span>
                    <span className="font-mono text-green-400">{localCalls?.outgoing.count}</span>
                  </div>
                  <Progress value={(localCalls?.outgoing.count / localCalls?.totalCalls) * 100} className="h-2" />

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-400">Incoming</span>
                    <span className="font-mono text-purple-400">{localCalls?.incoming.count}</span>
                  </div>
                  <Progress value={(localCalls?.incoming.count / localCalls?.totalCalls) * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-yellow-400">
                  <TrendingUp className="w-5 h-5 mr-2" />
                  Usage Patterns
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Peak Hour</span>
                  <Badge className="bg-yellow-900/20 text-yellow-400 border-yellow-400/20">
                    {localCalls?.peakHour}
                  </Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">I/O Ratio</span>
                  <span className="font-mono text-purple-400">{localCalls?.ratioIncomingOutgoing}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Distinct Ratio</span>
                  <span className="font-mono text-cyan-400">{localCalls?.distinctRatio}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Avg Duration</span>
                  <span className="font-mono text-orange-400">{localCalls?.avgDurationMin} min</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-cyan-400">
                <Users className="w-5 h-5 mr-2" />
                Contact Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-medium text-white">Outgoing Calls</h4>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Total Calls</span>
                    <span className="font-mono text-green-400">{localCalls?.outgoing.count}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Distinct Numbers</span>
                    <span className="font-mono text-green-400">{localCalls?.outgoing.distinctNumbers}</span>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium text-white">Incoming Calls</h4>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Total Calls</span>
                    <span className="font-mono text-purple-400">{localCalls?.incoming.count}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Distinct Numbers</span>
                    <span className="font-mono text-purple-400">{localCalls?.incoming.distinctNumbers}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="international" className="space-y-6">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-green-400">
                <Globe className="w-5 h-5 mr-2" />
                International Call Activity
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <p className="text-4xl font-bold text-green-400 mb-2">{internationalCalls?.totalCalls}</p>
                <p className="text-gray-400">Total International Calls</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium text-white flex items-center">
                    <PhoneOutgoing className="w-4 h-4 mr-2 text-green-400" />
                    Outgoing International
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Calls</span>
                      <span className="font-mono text-green-400">{internationalCalls?.outgoing.count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Unique Numbers</span>
                      <span className="font-mono text-green-400">{internationalCalls?.outgoing.uniqueNumbers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Countries</span>
                      <span className="font-mono text-green-400">{internationalCalls?.outgoing.countries}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-white flex items-center">
                    <PhoneIncoming className="w-4 h-4 mr-2 text-blue-400" />
                    Incoming International
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Calls</span>
                      <span className="font-mono text-blue-400">{internationalCalls?.incoming.count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Unique Numbers</span>
                      <span className="font-mono text-blue-400">{internationalCalls?.incoming.uniqueNumbers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Countries</span>
                      <span className="font-mono text-blue-400">{internationalCalls?.incoming.countries}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-purple-400">Call Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Local Calls</span>
                    <span className="font-mono text-blue-400">{localCalls?.totalCalls}</span>
                  </div>
                  <Progress value={(localCalls?.totalCalls / totalCalls) * 100} className="h-3" />

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">International Calls</span>
                    <span className="font-mono text-green-400">{internationalCalls?.totalCalls}</span>
                  </div>
                  <Progress value={(internationalCalls?.totalCalls / totalCalls) * 100} className="h-3" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-orange-400">Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Call Success Rate</span>
                  <Badge className="bg-green-900/20 text-green-400 border-green-400/20">98.5%</Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Network Quality</span>
                  <Badge className="bg-blue-900/20 text-blue-400 border-blue-400/20">Excellent</Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Drop Rate</span>
                  <span className="font-mono text-red-400">1.5%</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Avg Setup Time</span>
                  <span className="font-mono text-yellow-400">2.3s</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
