"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Phone,
  MessageSquare,
  Radio,
  CreditCard,
  Users,
  Download,
  TrendingUp,
  TrendingDown,
  Activity,
  AlertTriangle,
  Shield,
  Eye,
  Smartphone,
  MapPin,
  Target,
} from "lucide-react"
import Link from "next/link"

export default function Dashboard() {
  const [data, setData] = useState<any>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [subscriberRes, rechargeRes, dealerRes, networkRes, localCallRes, internationalCallRes, smsRes, dataRes, imeiRes] =
          await Promise.all([
            fetch("/api/subscriber-profile.json"),
            fetch("/api/recharge-payment.json"),
            fetch("/api/dealer-activations.json"),
            fetch("/api/network-usage.json"),
            fetch("/api/local-call-activity.json"),
            fetch("/api/international-call-activity.json"),
            fetch("/api/sms-activity.json"),
            fetch("/api/data-activity.json"),
            fetch("/api/imei-tracking.json").catch(() => null),
          ])

        const [
          subscriberData,
          rechargeData,
          dealerData,
          networkData,
          localCallData,
          internationalCallData,
          smsData,
          dataData,
          imeiData,
        ] = await Promise.all([
          subscriberRes.json(),
          rechargeRes.json(),
          dealerRes.json(),
          networkRes.json(),
          localCallRes.json(),
          internationalCallRes.json(),
          smsRes.json(),
          dataRes.json(),
          imeiRes ? imeiRes.json() : Promise.resolve(null),
        ])

        setData({
          subscriber: subscriberData,
          recharge: rechargeData,
          dealer: dealerData,
          network: networkData,
          localCall: localCallData,
          internationalCall: internationalCallData,
          sms: smsData,
          dataActivity: dataData,
          imei: imeiData,
        })
      } catch (error) {
        console.error("Error fetching data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  // Calculate overall fraud risk score
  const calculateOverallRiskScore = () => {
    const localRisk = data.localCall?.riskScore || 0
    const internationalRisk = data.internationalCall?.riskScore || 0
    const imeiRisk = data.imei?.riskScore || 0
    const subscriberRisk = data.subscriber?.fraudProfile?.overallRiskScore || 0

    return ((localRisk + internationalRisk + imeiRisk + subscriberRisk) / 4).toFixed(1)
  }

  const overallRiskScore = calculateOverallRiskScore()
  const getRiskLevel = (score: number) => {
    if (score >= 7) return { level: "HIGH", color: "text-red-400", bgColor: "bg-red-900/20", borderColor: "border-red-400/20" }
    if (score >= 4) return { level: "MEDIUM", color: "text-yellow-400", bgColor: "bg-yellow-900/20", borderColor: "border-yellow-400/20" }
    return { level: "LOW", color: "text-green-400", bgColor: "bg-green-900/20", borderColor: "border-green-400/20" }
  }

  const riskInfo = getRiskLevel(parseFloat(overallRiskScore))

  return (
    <div className="p-6 space-y-6">
      {/* Fraud Alert Banner */}
      {parseFloat(overallRiskScore) >= 7 && (
        <Card className="bg-red-900/20 border-red-400/20">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <AlertTriangle className="w-6 h-6 text-red-400" />
              <div>
                <p className="text-red-400 font-semibold">🚨 HIGH FRAUD RISK DETECTED</p>
                <p className="text-red-300 text-sm">Subscriber {data.subscriber?.msisdn} requires immediate investigation. Multiple suspicious patterns identified.</p>
              </div>
              <Link href="/calls">
                <Badge className="bg-red-900/30 text-red-400 border-red-400/30 hover:bg-red-900/40 cursor-pointer">
                  Investigate →
                </Badge>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            Fraud Analytics Dashboard
          </h1>
          <p className="text-gray-400 mt-2">
            Real-time fraud monitoring for subscriber {data.subscriber?.msisdn}
            <span className={`ml-2 font-semibold ${riskInfo.color}`}>
              • Risk Score: {overallRiskScore}/10 ({riskInfo.level})
            </span>
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <Badge className={`${riskInfo.bgColor} ${riskInfo.color} ${riskInfo.borderColor}`}>
            <Shield className="w-4 h-4 mr-1" />
            {riskInfo.level} RISK
          </Badge>
          <Badge variant="secondary" className="bg-green-900/20 text-green-400 border-green-400/20">
            <Activity className="w-4 h-4 mr-1" />
            Live Data
          </Badge>
        </div>
      </div>

      {/* Fraud Risk Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <Link href="/calls">
          <Card className={`bg-gray-900 border-gray-800 ${riskInfo.borderColor} hover:border-opacity-75 transition-colors cursor-pointer`}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Fraud Risk Score</p>
                  <p className={`text-2xl font-bold ${riskInfo.color}`}>{overallRiskScore}/10</p>
                  <Badge className={`${riskInfo.bgColor} ${riskInfo.color} ${riskInfo.borderColor} text-xs mt-1`}>
                    {riskInfo.level} RISK
                  </Badge>
                </div>
                <Shield className={`w-8 h-8 ${riskInfo.color}`} />
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/calls">
          <Card className="bg-gray-900 border-gray-800 hover:border-red-500/50 transition-colors cursor-pointer">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Short Calls</p>
                  <p className="text-2xl font-bold text-red-400">{data.localCall?.fraudIndicators?.shortCallsCount || 0}</p>
                  <div className="flex items-center mt-1">
                    <AlertTriangle className="w-4 h-4 text-red-400 mr-1" />
                    <p className="text-sm text-red-400">{data.localCall?.fraudIndicators?.shortCallsPercentage || 0}% of calls</p>
                  </div>
                </div>
                <Phone className="w-8 h-8 text-red-400" />
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/calls">
          <Card className="bg-gray-900 border-gray-800 hover:border-orange-500/50 transition-colors cursor-pointer">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">IMEI Changes</p>
                  <p className="text-2xl font-bold text-orange-400">{data.imei?.fraudIndicators?.totalIMEICount || 0}</p>
                  <div className="flex items-center mt-1">
                    <Smartphone className="w-4 h-4 text-orange-400 mr-1" />
                    <p className="text-sm text-orange-400">{data.imei?.fraudIndicators?.imeiSwitchFrequency || 'NORMAL'}</p>
                  </div>
                </div>
                <Smartphone className="w-8 h-8 text-orange-400" />
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/calls">
          <Card className="bg-gray-900 border-gray-800 hover:border-purple-500/50 transition-colors cursor-pointer">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Contact Network</p>
                  <p className="text-2xl font-bold text-purple-400">{data.localCall?.contactNetwork?.totalUniqueContacts || 0}</p>
                  <div className="flex items-center mt-1">
                    <Users className="w-4 h-4 text-purple-400 mr-1" />
                    <p className="text-sm text-purple-400">{((data.localCall?.contactNetwork?.networkDensity || 0) * 100).toFixed(0)}% density</p>
                  </div>
                </div>
                <Users className="w-8 h-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/calls">
          <Card className="bg-gray-900 border-gray-800 hover:border-cyan-500/50 transition-colors cursor-pointer">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Geographic Risk</p>
                  <p className="text-2xl font-bold text-cyan-400">{data.localCall?.fraudIndicators?.geographicMovement?.cellTowerSwitches || 0}</p>
                  <div className="flex items-center mt-1">
                    <MapPin className="w-4 h-4 text-cyan-400 mr-1" />
                    <p className="text-sm text-cyan-400">Cell switches</p>
                  </div>
                </div>
                <MapPin className="w-8 h-8 text-cyan-400" />
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* Active Fraud Alerts */}
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="flex items-center text-red-400">
            <AlertTriangle className="w-5 h-5 mr-2" />
            Active Fraud Alerts & Suspicious Patterns
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Short Call Pattern Alert */}
            {(data.localCall?.fraudIndicators?.shortCallsPercentage || 0) > 25 && (
              <div className="p-4 bg-red-900/10 rounded-lg border border-red-400/20">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-red-400 font-medium">Short Call Bursts</span>
                  <Badge className="bg-red-900/20 text-red-400 border-red-400/20">HIGH</Badge>
                </div>
                <p className="text-red-300 text-sm">
                  {data.localCall?.fraudIndicators?.shortCallsPercentage}% of calls under 1 minute
                </p>
                <p className="text-gray-400 text-xs mt-1">
                  {data.localCall?.fraudIndicators?.shortCallsCount} short calls detected
                </p>
              </div>
            )}

            {/* IMEI Switching Alert */}
            {(data.imei?.fraudIndicators?.totalIMEICount || 0) > 2 && (
              <div className="p-4 bg-orange-900/10 rounded-lg border border-orange-400/20">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-orange-400 font-medium">Device Switching</span>
                  <Badge className="bg-orange-900/20 text-orange-400 border-orange-400/20">
                    {data.imei?.fraudIndicators?.imeiSwitchFrequency}
                  </Badge>
                </div>
                <p className="text-orange-300 text-sm">
                  {data.imei?.fraudIndicators?.totalIMEICount} different devices used
                </p>
                <p className="text-gray-400 text-xs mt-1">
                  Avg usage: {data.imei?.fraudIndicators?.avgIMEIUsageDays} days per device
                </p>
              </div>
            )}

            {/* High Contact Ratio Alert */}
            {(data.localCall?.fraudIndicators?.distinctNumberRatio || 0) > 0.8 && (
              <div className="p-4 bg-yellow-900/10 rounded-lg border border-yellow-400/20">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-yellow-400 font-medium">High Contact Diversity</span>
                  <Badge className="bg-yellow-900/20 text-yellow-400 border-yellow-400/20">MEDIUM</Badge>
                </div>
                <p className="text-yellow-300 text-sm">
                  {(data.localCall?.fraudIndicators?.distinctNumberRatio * 100).toFixed(0)}% unique contacts
                </p>
                <p className="text-gray-400 text-xs mt-1">
                  {data.localCall?.contactNetwork?.totalUniqueContacts} different numbers contacted
                </p>
              </div>
            )}

            {/* Geographic Movement Alert */}
            {(data.localCall?.fraudIndicators?.geographicMovement?.cellTowerSwitches || 0) > 3 && (
              <div className="p-4 bg-cyan-900/10 rounded-lg border border-cyan-400/20">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-cyan-400 font-medium">High Mobility</span>
                  <Badge className="bg-cyan-900/20 text-cyan-400 border-cyan-400/20">WATCH</Badge>
                </div>
                <p className="text-cyan-300 text-sm">
                  {data.localCall?.fraudIndicators?.geographicMovement?.cellTowerSwitches} cell tower switches
                </p>
                <p className="text-gray-400 text-xs mt-1">
                  Max speed: {data.localCall?.fraudIndicators?.geographicMovement?.maxSpeedKmh} km/h
                </p>
              </div>
            )}

            {/* Volume Spike Alert */}
            {(data.localCall?.fraudIndicators?.highVolumeSpikes || 0) > 0 && (
              <div className="p-4 bg-purple-900/10 rounded-lg border border-purple-400/20">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-purple-400 font-medium">Volume Spikes</span>
                  <Badge className="bg-purple-900/20 text-purple-400 border-purple-400/20">MEDIUM</Badge>
                </div>
                <p className="text-purple-300 text-sm">
                  {data.localCall?.fraudIndicators?.highVolumeSpikes} unusual activity periods
                </p>
                <p className="text-gray-400 text-xs mt-1">
                  Max: {data.localCall?.fraudIndicators?.maxCallsInHour} calls in one hour
                </p>
              </div>
            )}

            {/* No Alerts State */}
            {!(
              (data.localCall?.fraudIndicators?.shortCallsPercentage || 0) > 25 ||
              (data.imei?.fraudIndicators?.totalIMEICount || 0) > 2 ||
              (data.localCall?.fraudIndicators?.distinctNumberRatio || 0) > 0.8 ||
              (data.localCall?.fraudIndicators?.geographicMovement?.cellTowerSwitches || 0) > 3 ||
              (data.localCall?.fraudIndicators?.highVolumeSpikes || 0) > 0
            ) && (
              <div className="p-4 bg-green-900/10 rounded-lg border border-green-400/20 col-span-full">
                <div className="flex items-center justify-center">
                  <Shield className="w-5 h-5 text-green-400 mr-2" />
                  <span className="text-green-400 font-medium">No Active Fraud Alerts</span>
                </div>
                <p className="text-green-300 text-sm text-center mt-1">
                  All patterns within normal parameters
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Link href="/calls">
          <Card className="bg-gray-900 border-gray-800 hover:border-blue-500/50 transition-colors cursor-pointer">
            <CardContent className="p-6 text-center">
              <Target className="w-12 h-12 text-blue-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Detailed Analysis</h3>
              <p className="text-gray-400 text-sm">Deep dive into call patterns and fraud indicators</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/sms">
          <Card className="bg-gray-900 border-gray-800 hover:border-purple-500/50 transition-colors cursor-pointer">
            <CardContent className="p-6 text-center">
              <MessageSquare className="w-12 h-12 text-purple-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">SMS Analysis</h3>
              <p className="text-gray-400 text-sm">Analyze messaging patterns and anomalies</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/data">
          <Card className="bg-gray-900 border-gray-800 hover:border-orange-500/50 transition-colors cursor-pointer">
            <CardContent className="p-6 text-center">
              <Download className="w-12 h-12 text-orange-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Data Usage</h3>
              <p className="text-gray-400 text-sm">Monitor bandwidth and usage patterns</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/recharge">
          <Card className="bg-gray-900 border-gray-800 hover:border-green-500/50 transition-colors cursor-pointer">
            <CardContent className="p-6 text-center">
              <CreditCard className="w-12 h-12 text-green-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Revenue Analysis</h3>
              <p className="text-gray-400 text-sm">Track payments and financial patterns</p>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  )
}
